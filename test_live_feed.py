#!/usr/bin/env python3
"""
Test script to verify the live feed implementation works correctly
"""

import sys
import os
import time
import threading
from unittest.mock import Mock, patch

# Add paths for imports
sys.path.append(os.path.dirname(__file__))
for path in ['core/fyers']:
    sys.path.append(os.path.join(os.path.dirname(__file__), path))

def test_message_processing():
    """Test the message processing logic"""
    
    # Mock the global variables and functions
    from app.socket_events import subscriber_rooms, subscribed_symbols, live_feeds
    
    # Clear any existing state
    subscriber_rooms.clear()
    subscribed_symbols.clear()
    live_feeds.clear()
    
    # Set up test data
    test_symbol = "NSE:RELIANCE-EQ"
    test_room_id = "test_client_chart-0_NSE:RELIANCE-EQ_5m_0.05_100"
    
    # Add test subscription
    subscriber_rooms[test_room_id] = {
        'symbol': test_symbol,
        'timeframe': '5m',
        'bucket_size': 0.05,
        'multiplier': 100,
        'chart_id': 'chart-0',
        'client_id': 'test_client'
    }
    
    subscribed_symbols.add(test_symbol)
    live_feeds[test_symbol] = {'subscribers': 1}
    
    print(f"Test setup complete:")
    print(f"  Subscribed symbols: {subscribed_symbols}")
    print(f"  Subscriber rooms: {list(subscriber_rooms.keys())}")
    print(f"  Live feeds: {list(live_feeds.keys())}")
    
    # Test message formats
    test_messages = [
        # Test 1: Single dict message
        {
            'symbol': test_symbol,
            'ltp': 2500.50,
            'exch_feed_time': int(time.time()),
            'vol_traded_today': 1000000,
            'last_traded_qty': 100
        },
        
        # Test 2: List of messages
        [
            {
                'symbol': test_symbol,
                'ltp': 2501.00,
                'exch_feed_time': int(time.time()),
                'vol_traded_today': 1000100,
                'last_traded_qty': 50
            }
        ],
        
        # Test 3: Invalid message (should be ignored)
        {
            'invalid': 'data'
        },
        
        # Test 4: Message for unsubscribed symbol (should be ignored)
        {
            'symbol': 'NSE:SBIN-EQ',
            'ltp': 800.00,
            'exch_feed_time': int(time.time()),
            'vol_traded_today': 500000,
            'last_traded_qty': 25
        }
    ]
    
    # Mock the socketio.emit function to capture emissions
    emitted_data = []
    
    def mock_emit(event, data, room=None):
        emitted_data.append({
            'event': event,
            'data': data,
            'room': room
        })
        print(f"EMIT: {event} to room {room}")
        print(f"  Data: {data}")
    
    # Mock the process_live_data function
    def mock_process_live_data(msg, timeframe, bucket_size, multiplier, ensure_continuity=True):
        if msg.get('symbol') == test_symbol and msg.get('ltp'):
            return {
                'time': int(time.time()),
                'open': msg['ltp'],
                'high': msg['ltp'],
                'low': msg['ltp'],
                'close': msg['ltp'],
                'volume': msg.get('last_traded_qty', 0),
                'delta': 0,
                'poc': msg['ltp']
            }
        return None
    
    # Apply mocks
    with patch('app.socket_events.socketio_instance') as mock_socketio:
        mock_socketio.emit = mock_emit
        
        with patch('core.fyers.processor.process_live_data', mock_process_live_data):
            # Import the function we want to test
            from app.socket_events import register_socket_events
            
            # Create a mock socketio app
            mock_app = Mock()
            register_socket_events(mock_app)
            
            # Get the global callback function by creating a mock feed
            from app.socket_events import start_global_feed
            
            # Mock the FyersDataFeed and WebSocket
            with patch('app.socket_events.FyersDataFeed') as mock_fyers:
                with patch('fyers_apiv3.FyersWebsocket.data_ws.FyersDataSocket') as mock_ws:
                    mock_fyers.return_value.access_token = "test_token"
                    
                    # Capture the callback function
                    callback_func = None
                    
                    def capture_callback(*args, **kwargs):
                        nonlocal callback_func
                        if 'on_message' in kwargs:
                            callback_func = kwargs['on_message']
                        return Mock()
                    
                    mock_ws.side_effect = capture_callback
                    
                    # Start the global feed
                    try:
                        start_global_feed()
                        print("Global feed started successfully")
                    except Exception as e:
                        print(f"Error starting global feed: {e}")
                        return
                    
                    # Test each message
                    for i, test_msg in enumerate(test_messages):
                        print(f"\n--- Testing message {i+1}: {type(test_msg)} ---")
                        
                        if callback_func:
                            try:
                                callback_func(test_msg)
                            except Exception as e:
                                print(f"Error processing message {i+1}: {e}")
                                import traceback
                                traceback.print_exc()
                        else:
                            print("No callback function captured")
    
    print(f"\n--- Test Results ---")
    print(f"Total emissions: {len(emitted_data)}")
    for i, emission in enumerate(emitted_data):
        print(f"Emission {i+1}: {emission['event']} to {emission['room']}")

if __name__ == "__main__":
    print("Testing live feed message processing...")
    test_message_processing()
    print("Test completed!")
